import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { isUserOnFreePlan } from '../../utils/auth/userHelpers';

interface FreePlanNoticeProps {
  storageKey?: string;
  variant?: 'full' | 'button';
}

const FreePlanNotice: React.FC<FreePlanNoticeProps> = ({
  storageKey = 'freePlanNoticeDismissed',
  variant = 'full',
}) => {
  const navigate = useNavigate();
  const [isDismissed, setIsDismissed] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Check if user is on free plan
  const isFreePlan = isUserOnFreePlan();

  useEffect(() => {
    const dismissed = localStorage.getItem(storageKey);
    if (dismissed === 'true') {
      setIsDismissed(true);
    }
  }, [storageKey]);

  const handleDismiss = () => {
    setIsDismissed(true);
    localStorage.setItem(storageKey, 'true');
  };

  const handleUpgrade = () => {
    // navigate('/price');
  };

  // Don't show if user is not on free plan or has dismissed
  if (!isFreePlan || isDismissed) {
    return null;
  }

  // Button variant for chat page
  if (variant === 'button') {
    return (
      <div
        style={{
          position: 'relative',
          display: 'inline-block',
          marginRight: '10px',
        }}
      >
        <button
          onClick={handleUpgrade}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          style={{
            background: 'linear-gradient(135deg, #ffc107 0%, #ff8c00 100%)',
            color: '#856404',
            border: 'none',
            borderRadius: '20px',
            padding: '8px 16px',
            fontSize: '13px',
            fontWeight: '600',
            cursor: 'pointer',
            boxShadow: '0 2px 6px rgba(255, 193, 7, 0.3)',
            transition: 'all 0.2s ease',
            fontFamily:
              '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
          }}
        >
          ⚠️ Upgrade to Pro
        </button>

        {isHovered && (
          <div
            style={{
              position: 'absolute',
              top: '100%',
              right: '0',
              marginTop: '8px',
              background: 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)',
              border: '1px solid #ffeeba',
              borderRadius: '8px',
              padding: '12px',
              boxShadow: '0 4px 12px rgba(255, 193, 7, 0.25)',
              zIndex: 1000,
              minWidth: '280px',
              fontFamily:
                '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            }}
          >
            <div
              style={{
                color: '#856404',
                fontSize: '14px',
                fontWeight: '600',
                marginBottom: '8px',
                // marginRight: '100px',
              }}
            >
              What’s included in Free Plan:
            </div>
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '4px',
                fontSize: '12px',
                color: '#856404',
              }}
            >
              <div>🚀 Explore all features — no restrictions!</div>
              <div>📎 Upload files up to 50KB</div>
              <div>📊 Excel & CSV: Limited query support</div>
              <div>🐢 Slightly slower responses during peak times</div>
              {/* <div>🧑‍💼 No priority support</div> */}
            </div>
          </div>
        )}
      </div>
    );
  }

  // Full variant for home page
  return (
    <div
      style={{
        background: 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)',
        border: '1px solid #ffeeba',
        borderRadius: '8px',
        padding: '12px 16px',
        margin: '12px 0',
        boxShadow: '0 2px 8px rgba(255, 193, 7, 0.15)',
        fontFamily:
          '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      }}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '8px',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ fontSize: '20px' }}>⚠️</span>
          <h3
            style={{
              margin: 0,
              color: '#856404',
              fontSize: '16px',
              fontWeight: '600',
            }}
          >
            Using the Free Plan – Here’s what’s included:
          </h3>
        </div>
        <button
          disabled
          onClick={handleDismiss}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '10px',
            cursor: 'pointer',
            color: '#856404',
            padding: '4px',
          }}
        >
          ×
        </button>
      </div>

      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          flexWrap: 'wrap',
          gap: '12px',
          alignItems: 'center',
          marginBottom: '0',
        }}
      >
        <span style={{ color: '#856404', fontSize: '13px' }}>
          🚀 Explore all features — no restrictions!
        </span>
        <span style={{ color: '#856404', fontSize: '13px' }}>
          📎 Upload files up to 50KB
        </span>
        <span style={{ color: '#856404', fontSize: '13px' }}>
          📊 Excel & CSV: Limited query support
        </span>
        <span style={{ color: '#856404', fontSize: '13px' }}>
          🐢 Slightly slower responses during peak times
        </span>

        <button
          onClick={handleUpgrade}
          style={{
            background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            padding: '8px 16px',
            fontSize: '14px',
            fontWeight: '600',
            cursor: 'pointer',
            boxShadow: '0 2px 4px rgba(0, 123, 255, 0.3)',
            marginLeft: 'auto',
          }}
        >
          Upgrade to Pro
        </button>
      </div>
    </div>
  );
};

export default FreePlanNotice;
