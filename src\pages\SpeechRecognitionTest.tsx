import React, { useState } from 'react';
import { useSpeechRecognition } from 'react-speech-recognition';
import VoiceInput from '../components/chat/VoiceInput';
import SpeechRecognitionDiagnostics from '../components/chat/SpeechRecognitionDiagnostics';
import { logSpeechRecognitionDiagnostics } from '../utils/speechRecognitionUtils';

const SpeechRecognitionTest: React.FC = () => {
  const { 
    transcript, 
    listening, 
    resetTranscript, 
    browserSupportsSpeechRecognition 
  } = useSpeechRecognition();
  
  const [isListening, setIsListening] = useState(false);
  const [showDiagnostics, setShowDiagnostics] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const runDiagnostics = async () => {
    addTestResult('Running diagnostics...');
    await logSpeechRecognitionDiagnostics();
    addTestResult('Diagnostics completed - check browser console');
  };

  const clearResults = () => {
    setTestResults([]);
    resetTranscript();
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>🎤 Speech Recognition Test Page</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <p>
          This page helps you test and debug speech recognition functionality.
          Use this to identify why speech-to-text might not be working in production.
        </p>
      </div>

      {/* Browser Support Status */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: browserSupportsSpeechRecognition ? '#e8f5e8' : '#ffebee',
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h3>Browser Support Status</h3>
        <p>
          <strong>Speech Recognition Supported:</strong> {' '}
          <span style={{ 
            color: browserSupportsSpeechRecognition ? '#4caf50' : '#f44336',
            fontWeight: 'bold'
          }}>
            {browserSupportsSpeechRecognition ? '✅ Yes' : '❌ No'}
          </span>
        </p>
        {!browserSupportsSpeechRecognition && (
          <p style={{ color: '#f44336' }}>
            Your browser doesn't support speech recognition. Try Chrome, Edge, or Safari.
          </p>
        )}
      </div>

      {/* Current Environment Info */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: '#f5f5f5',
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h3>Current Environment</h3>
        <ul>
          <li><strong>Protocol:</strong> {window.location.protocol}</li>
          <li><strong>Hostname:</strong> {window.location.hostname}</li>
          <li><strong>Port:</strong> {window.location.port || 'default'}</li>
          <li><strong>User Agent:</strong> {navigator.userAgent}</li>
          <li><strong>Is Secure Context:</strong> {
            window.isSecureContext ? '✅ Yes' : '❌ No'
          }</li>
        </ul>
      </div>

      {/* Voice Input Test */}
      {browserSupportsSpeechRecognition && (
        <div style={{ 
          padding: '15px', 
          backgroundColor: '#e3f2fd',
          borderRadius: '8px',
          marginBottom: '20px'
        }}>
          <h3>Voice Input Test</h3>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '10px',
            marginBottom: '10px'
          }}>
            <VoiceInput 
              isListening={isListening} 
              setListening={setIsListening} 
            />
            <span>
              Status: {isListening ? '🔴 Listening...' : '⚪ Not listening'}
            </span>
          </div>
          
          <div style={{ marginBottom: '10px' }}>
            <strong>Current Transcript:</strong>
            <div style={{ 
              padding: '10px', 
              backgroundColor: 'white', 
              border: '1px solid #ddd',
              borderRadius: '4px',
              minHeight: '40px',
              marginTop: '5px'
            }}>
              {transcript || '(No speech detected yet)'}
            </div>
          </div>
          
          <button 
            onClick={resetTranscript}
            style={{
              padding: '8px 16px',
              backgroundColor: '#ff9800',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Clear Transcript
          </button>
        </div>
      )}

      {/* Diagnostic Tools */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: '#fff3e0',
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h3>Diagnostic Tools</h3>
        <div style={{ display: 'flex', gap: '10px', marginBottom: '10px' }}>
          <button 
            onClick={runDiagnostics}
            style={{
              padding: '8px 16px',
              backgroundColor: '#2196f3',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Run Diagnostics
          </button>
          
          <button 
            onClick={() => setShowDiagnostics(!showDiagnostics)}
            style={{
              padding: '8px 16px',
              backgroundColor: '#9c27b0',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            {showDiagnostics ? 'Hide' : 'Show'} Detailed Diagnostics
          </button>
          
          <button 
            onClick={clearResults}
            style={{
              padding: '8px 16px',
              backgroundColor: '#f44336',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Clear Results
          </button>
        </div>
      </div>

      {/* Detailed Diagnostics */}
      {showDiagnostics && (
        <div style={{ marginBottom: '20px' }}>
          <SpeechRecognitionDiagnostics 
            onClose={() => setShowDiagnostics(false)}
          />
        </div>
      )}

      {/* Test Results */}
      {testResults.length > 0 && (
        <div style={{ 
          padding: '15px', 
          backgroundColor: '#f5f5f5',
          borderRadius: '8px'
        }}>
          <h3>Test Results</h3>
          <div style={{ 
            backgroundColor: 'white', 
            padding: '10px',
            borderRadius: '4px',
            border: '1px solid #ddd',
            maxHeight: '200px',
            overflowY: 'auto'
          }}>
            {testResults.map((result, index) => (
              <div key={index} style={{ 
                marginBottom: '5px',
                fontFamily: 'monospace',
                fontSize: '12px'
              }}>
                {result}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: '#e8f5e8',
        borderRadius: '8px',
        marginTop: '20px'
      }}>
        <h3>How to Use This Test Page</h3>
        <ol>
          <li>Check the "Browser Support Status" section first</li>
          <li>Review the "Current Environment" to see if you're using HTTPS</li>
          <li>Click "Run Diagnostics" to get detailed information</li>
          <li>Try the voice input test if supported</li>
          <li>Check browser console for additional debug information</li>
        </ol>
        
        <h4>Common Issues:</h4>
        <ul>
          <li><strong>HTTP instead of HTTPS:</strong> Speech recognition requires a secure connection</li>
          <li><strong>Unsupported browser:</strong> Use Chrome, Edge, or Safari</li>
          <li><strong>Microphone permission:</strong> Allow microphone access when prompted</li>
          <li><strong>Network issues:</strong> Check internet connection</li>
        </ul>
      </div>
    </div>
  );
};

export default SpeechRecognitionTest;
