import { useState, memo, useMemo, useEffect } from 'react';
import Card from '../../components/common/card/Card';
import style from './HomePage.module.css';
import profile from '../../assets/images/profile.png';
import ChatInput from '../../components/specific/ChatInput/ChatInput';
import { useNavigate } from 'react-router-dom';
import CircularLoader from '../../assets/loader/CircularLoader';
import { PredefinedPrompts } from '../../components/specific/ChatInput/PredefinedPrompts';
import type { PredefinedPrompt } from '../../types/predefinedPrompts';
import { useDropzone } from 'react-dropzone';
import { FiUploadCloud } from 'react-icons/fi';
import {
  useDeleteProjectByIdMutation,
  useGetRecentProjectsPaginatedQuery,
} from '../../services/projectsService';
import WelcomeDialog from '../../components/common/WelcomeDialog';
import useUIPreferences from '../../hooks/useUIPreferences';
import useLocalStorage from '../../hooks/useLocalStorage';
import { getTimeAgo } from '../../utils/timesAgo';
import { BsGrid, BsList } from 'react-icons/bs';
import ListCard from '../../components/common/card/ListCard';

import ProjectDialog from '../../components/common/ProjectDialog/ProjectDialog';
import { useTheme } from '../../hooks/useTheme';
import { useUploadFileMutation } from '../../services/chatServices';
import { countPagesInFile } from '../../utils/pageCounter';
import { getSimplifiedFileExtension } from '../../utils/getFileExtention';
import { createFormData } from '../../utils/formDataHelper';
import toast from 'react-hot-toast';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store/store';
import { checkFileInBrowser } from '../../utils/extractFileSize';
import FreePlanNotice from '../../components/common/FreePlanNotice';

const categories = [
  { id: 'featured', label: 'Featured', icon: '✨' },
  { id: 'business_strategy', label: 'Business & Strategy', icon: '📊' },
  { id: 'finance_accounting', label: 'Finance & Accounting', icon: '📈' },
  { id: 'legal_compliance', label: 'Legal & Compliance', icon: '⚖️' },
  { id: 'medical_healthcare', label: 'Medical & Healthcare', icon: '🩺' },
  { id: 'academic_research', label: 'Academic Research', icon: '🎓' },
];

const CategoryTab = memo(
  ({
    category,
    isActive,
    onClick,
    theme,
    id,
  }: {
    category: (typeof categories)[0];
    isActive: boolean;
    onClick: () => void;
    theme: any;
    id: string;
  }) => (
    <button
      id={id}
      key={category.id}
      className={`${style.categoryTab} ${isActive ? style.active : ''}`}
      onClick={onClick}
      style={{
        backgroundColor: isActive
          ? `${theme.colors.primary.light}10`
          : theme.colors.grey[100],
        color: isActive
          ? 'var(--color-primary-light)'
          : theme.colors.text.secondary,
        fontFamily: theme.typography.fontFamily.primary,
        fontWeight: isActive
          ? theme.typography.fontWeight.semibold
          : theme.typography.fontWeight.medium,
        border: isActive ? `1px solid ${theme.colors.primary.main}` : 'none',
      }}
    >
      <span className={style.categoryIcon}>{category.icon}</span>{' '}
      {category.label}
    </button>
  )
);

CategoryTab.displayName = 'CategoryTab';

const HomePage = () => {
  const userDetails = useLocalStorage('user', null);
  const userId = userDetails?.[0]?.id;
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState<
    'uploading' | 'processing' | null
  >(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedPrompt, setSelectedPrompt] = useState<PredefinedPrompt | null>(
    null
  );
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [title, setTitle] = useState('');
  const [rangeInput, setRangeInput] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('featured');
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [showWelcome, setShowWelcome] = useState(false);

  // Use the UI preferences hook for persistent state
  const { viewMode, setViewMode, activeTab, setActiveTab } = useUIPreferences({
    pageKey: 'homepage',
    defaultViewMode: 'grid',
    defaultActiveTab: 'templates',
  });

  // Check for first-time user and show welcome dialog
  useEffect(() => {
    const hasSeenWelcome = localStorage.getItem('hasSeenWelcome');

    if (hasSeenWelcome === null || hasSeenWelcome === 'false') {
      console.log('HomePage - Setting showWelcome to true');
      setShowWelcome(true);
    }
  }, []);

  const handleWelcomeClose = () => {
    console.log('HomePage - WelcomeDialog closing');
    localStorage.setItem('hasSeenWelcome', 'true');
    setShowWelcome(false);
  };

  // Function to get engaging upload messages based on progress percentage
  const getCurrentUploadMessage = (progress: number): string => {
    // Array of engaging messages for different progress stages
    let messages: string[] = [];

    if (progress === 0) {
      return 'Preparing your file for upload...';
    } else if (progress >= 100) {
      messages = [
        'Upload complete! Now processing your file...',
        'Your file made it! Now working our magic...',
        'Upload successful! Preparing your data...',
        'Great job! Your file is now being processed...',
      ];
    } else if (progress >= 76) {
      messages = [
        'Final stretch! Your file is almost home!',
        'So close now! Just a few more moments...',
        'Success is just around the corner!',
        'The upload is nearly complete! Hang in there!',
      ];
    } else if (progress >= 51) {
      messages = [
        'More than halfway there! Your file is a champion!',
        'The finish line is coming into view!',
        'Your file is performing excellently!',
        'Almost there! Your file is working hard!',
      ];
    } else if (progress >= 26) {
      messages = [
        'Making good progress! Keep the faith!',
        'Your file is cruising along nicely!',
        "We're getting there! Your file is halfway to its destination!",
        'Halfway point reached! Your file is doing great!',
      ];
    } else if (progress >= 11) {
      messages = [
        'Your file is making progress! 🚀',
        'Transferring your valuable data...',
        'Bits and bytes are flowing smoothly!',
        'Your file is on its way to greatness!',
      ];
    } else {
      messages = [
        'Starting the journey of your file to our servers...',
        'Initiating upload sequence...',
        'Your file is getting ready for its big adventure!',
        'Warming up the upload engines...',
      ];
    }

    // Use a stable index for each progress category to avoid message flicker
    const stableIndex = currentMessageIndex % messages.length;
    return messages[stableIndex];
  };

  // Fetch all projects
  const {
    data: projectsData,
    isLoading,
    refetch: fetchProjects,
    isError,
  } = useGetRecentProjectsPaginatedQuery(
    { userId, page: 0, size: 8 },
    {
      refetchOnMountOrArgChange: true,
    }
  );

  // Filter projects based on active tab
  const filteredProjectsData = useMemo(() => {
    if (!projectsData?.data?.content) return projectsData;

    const filteredContent = projectsData.data.content.filter((project: any) => {
      if (activeTab === 'templates') {
        return project.is_templated_project === true;
      } else {
        return project.is_templated_project !== true;
      }
    });

    // Limit to 4 items for homepage display
    const limitedContent = filteredContent.slice(0, 4);

    return {
      ...projectsData,
      data: {
        ...projectsData.data,
        content: limitedContent,
      },
    };
  }, [projectsData, activeTab]);

  const [deleteProject] = useDeleteProjectByIdMutation();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);

  const navigate = useNavigate();
  const [inputValue, setInputValue] = useState('');
  const [isCustomPrompt] = useState(false);
  const theme = useTheme();

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        ['.docx'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
        '.xlsx',
      ],
      'text/csv': ['.csv'],
      'text/plain': ['.txt'],
    },
    noClick: true,
    onDrop: (acceptedFiles) => {
      if (acceptedFiles?.[0]) {
        handleFileUpload(acceptedFiles[0]);
      }
    },
  });

  const [uploadFile] = useUploadFileMutation();
  const responseType = useSelector(
    (state: RootState) => state.settings.responseType
  );

  const handleSend = () => {
    const projectId = `project_${new Date().toISOString().replace(/[-:.TZ]/g, '')}`;
    navigate('/chat', { state: { inputMessage: inputValue, projectId } });
    setInputValue('');
  };

  const handleCardClick = (recentProject: any) => {
    navigate('/chat', { state: { recentProject } });
  };

  const handlePromptSelect = (prompt: PredefinedPrompt) => {
    setSelectedPrompt(prompt);
    console.log(prompt, 'hello Prompt testing');
    setIsDialogOpen(true);
  };

  const handleFileUpload = (file: File) => {
    setUploadedFile(file);
    setIsDialogOpen(true);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setSelectedPrompt(null);
    setUploadedFile(null);
    setTitle('');
  };

  const handleUploadSubmit = async (
    projectId: string,
    pageRanges: any,
    promptText: string,
    file?: File,
    promptId?: string
  ) => {
    try {
      const fileToUpload = file || uploadedFile;

      if (!fileToUpload) {
        throw new Error('Please select a file');
      }

      if (!projectId) {
        throw new Error('Project ID is required');
      }

      if (!userId) {
        throw new Error('User ID is required');
      }

      setUploadStatus('uploading');
      // Set a random message index at the start of upload
      setCurrentMessageIndex(Math.floor(Math.random() * 4));

      const numberOfPages = await countPagesInFile(fileToUpload);
      const fileExtension = getSimplifiedFileExtension(fileToUpload);
      const fileSizeSupportForFreeUsers =
        await checkFileInBrowser(fileToUpload);

      if (!fileSizeSupportForFreeUsers.isAllowed) {
        toast.error(fileSizeSupportForFreeUsers.message, {
          duration: 6000,
        });
        return;
      }
      if (!fileExtension) {
        throw new Error('Invalid file type');
      }

      const formData = createFormData({
        userId,
        projectId,
        pageNumber: rangeInput,
        fileName: fileToUpload.name,
        fileType: fileExtension,
        title,
        prompt: promptText || ' ',
        file: fileToUpload,
        response_type: responseType,
        custom_prompt_name: promptId || '',
      });

      const result = await uploadFile({
        formData,
        onProgress: (progress: number, status: 'uploading' | 'processing') => {
          // Check if we're crossing a progress threshold and update the message index if needed
          const newCategory =
            progress >= 100
              ? 5
              : progress >= 76
                ? 4
                : progress >= 51
                  ? 3
                  : progress >= 26
                    ? 2
                    : progress >= 11
                      ? 1
                      : 0;

          const oldCategory =
            uploadProgress >= 100
              ? 5
              : uploadProgress >= 76
                ? 4
                : uploadProgress >= 51
                  ? 3
                  : uploadProgress >= 26
                    ? 2
                    : uploadProgress >= 11
                      ? 1
                      : 0;

          if (newCategory !== oldCategory) {
            // When crossing a threshold, pick a new random message
            setCurrentMessageIndex(Math.floor(Math.random() * 4));
          }

          setUploadProgress(progress);
          setUploadStatus(status);
        },
      }).unwrap();

      const uploadResponse = result as any;
      const customPropmt = promptText.split('|||').pop()?.trim() ?? '';
      toast.success('File Uploaded', { duration: 6000 });
      navigate('/chat', {
        state: {
          summary: uploadResponse.summary,
          tables: uploadResponse.tables,
          questions: uploadResponse.suggested_questions,
          file: fileToUpload,
          numberOfPages,
          insightProjectId: uploadResponse.object_id,
          projectId,
          pageRange: pageRanges,
          uploadFileTitle: title,
          fileUpload: true,
          isCustomPrompt,
          customPromptContent: customPropmt,
        },
      });
    } catch (error) {
      console.error('File upload failed:', error);
      toast.error(
        error instanceof Error ? error.message : 'File upload failed',
        {
          duration: 6000,
        }
      );
    } finally {
      setUploadStatus(null);
      handleDialogClose();
    }
  };

  // Use the filtered data directly (no need for useGridData on homepage since no pagination)
  const filteredProjects = filteredProjectsData?.data?.content || [];

  const userArray = [
    { name: 'Manzoor', avatar: profile },
    { name: 'Manzoor', avatar: profile },
    { name: 'Manzoor', avatar: profile },
    { name: 'Manzoor', avatar: profile },
  ];

  const getInitials = (title: string) => {
    const words = title.split(' ');
    const firstChar = words[0].charAt(0).toUpperCase();
    const secondChar = words.length > 1 ? words[1].charAt(0).toUpperCase() : '';
    return firstChar + secondChar;
  };

  const handleDelete = (projectId: string) => {
    setProjectToDelete(projectId);
    setShowDeleteConfirm(true);
  };

  const handleProjectDelete = async (projectId: string) => {
    try {
      // Execute the delete API call
      await deleteProject(projectId).unwrap();
      // Refresh the projects list after successful deletion
    } catch (error) {
      console.error('Error deleting project:', error);
      alert('Failed to delete project. Please try again.');
    }
  };

  const confirmDelete = () => {
    if (projectToDelete) {
      handleProjectDelete(projectToDelete);
      setShowDeleteConfirm(false);
      setProjectToDelete(null);
      // Refresh data
      fetchProjects();
    }
  };

  const cancelDelete = () => {
    setShowDeleteConfirm(false);
    setProjectToDelete(null);
  };

  return (
    <div
      className={`${style.homeContainer} ${isDragActive ? style.dragActive : ''}`}
      {...getRootProps()}
    >
      <input {...getInputProps()} />
      {isDragActive && (
        <div className={style.dropOverlay}>
          <FiUploadCloud size={48} />
          <p>Drop your file here to upload</p>
        </div>
      )}

      {(uploadStatus === 'uploading' || uploadStatus === 'processing') && (
        <div className={style.uploadProgress}>
          <div className={style.progressBar}>
            <div
              className={style.progressFill}
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
          <p>
            {uploadStatus === 'processing'
              ? 'Processing your file...'
              : `${uploadProgress}% - ${getCurrentUploadMessage(uploadProgress)}`}
          </p>
        </div>
      )}

      <div className={style.topSection}>
        <div className={style.chatInputContainer}>
          <h2
            className={style.chatInputHeading}
            style={{
              fontFamily: theme.typography.fontFamily.primary,
              color: theme.colors.primary.light,
            }}
          >
            AI-Powered Document Analysis & Insights
          </h2>
        </div>

        <FreePlanNotice storageKey="homePageFreePlanNotice" />

        <div id="catagoryTabs" className={style.categoryTabs}>
          {categories.map((category) => (
            <CategoryTab
              id={category.id}
              key={category.id}
              category={category}
              isActive={selectedCategory === category.id}
              onClick={() => setSelectedCategory(category.id)}
              theme={theme}
            />
          ))}
        </div>
        <PredefinedPrompts
          onPromptSelect={handlePromptSelect}
          selectedCategory={selectedCategory}
        />

        <div className={style.recentProjects}>
          <div className={style.recentProjectsHeading}>
            <h3 className={style.recentProjectsHeadingText}>Projects</h3>
            <div className={style.projectControls}>
              <div className={style.viewToggle}>
                <button
                  className={`${style.viewButton} ${viewMode === 'grid' ? style.active : ''}`}
                  onClick={() => setViewMode('grid')}
                  aria-label="Grid view"
                >
                  <BsGrid size={16} /> Grid
                </button>
                <button
                  className={`${style.viewButton} ${viewMode === 'list' ? style.active : ''}`}
                  onClick={() => setViewMode('list')}
                  aria-label="List view"
                >
                  <BsList size={16} /> List
                </button>
              </div>
              <button
                className={style.viewAllButton}
                onClick={() => navigate('/my-projects')}
              >
                View All
              </button>
            </div>
          </div>

          <div className={style.projectTabs}>
            <button
              className={`${style.projectTab} ${activeTab === 'templates' ? style.activeTab : ''}`}
              onClick={() => setActiveTab('templates')}
            >
              Templates
            </button>
            <button
              className={`${style.projectTab} ${activeTab === 'recent' ? style.activeTab : ''}`}
              onClick={() => setActiveTab('recent')}
            >
              Recent Projects
            </button>
          </div>

          {isLoading ? (
            <div className={style.loaderContainer}>
              <CircularLoader />
            </div>
          ) : isError ? (
            <div className={style.projectError}>
              <p>Error loading projects</p>
            </div>
          ) : filteredProjects?.length > 0 ? (
            <div className={`${style.projects} ${style[viewMode]}`}>
              {filteredProjects.map((project: any) => {
                return viewMode === 'grid' ? (
                  <Card
                    key={project.id}
                    type="project"
                    title={project.title}
                    initials={getInitials(project.title)}
                    users={userArray}
                    timeAgo={getTimeAgo(project.timestamp)}
                    status="Published"
                    role="creator"
                    handleCardClick={() => handleCardClick(project)}
                    fromProject={true}
                    onDelete={() => handleDelete(project.id)}
                  />
                ) : (
                  <ListCard
                    key={project.id}
                    title={project.title}
                    initials={getInitials(project.title)}
                    users={userArray}
                    timeAgo={getTimeAgo(project.timestamp)}
                    status="Published"
                    role="creator"
                    handleCardClick={() => handleCardClick(project)}
                    fromProject={true}
                    onDelete={() => handleDelete(project.id)}
                  />
                );
              })}
            </div>
          ) : (
            <div className={style.emptyState}>
              <h2>No projects found</h2>
              <p>Upload a document to start a new project</p>
            </div>
          )}
        </div>

        <div className={style.chatinputWrapper}>
          <ChatInput
            type="inputWithFileUpload"
            placeholder="Start a direct conversation or drop a file..."
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onSend={handleSend}
            onFileUpload={handleFileUpload}
          />
        </div>
      </div>

      {isDialogOpen && (
        <ProjectDialog
          onClose={handleDialogClose}
          onSelectProject={(
            projectId,
            pageRanges,
            promptText,
            file,
            promptId
          ) =>
            handleUploadSubmit(
              projectId,
              pageRanges,
              promptText,
              file,
              promptId
            )
          }
          rangeInput={rangeInput}
          setRangeInput={setRangeInput}
          title={title}
          setTitle={setTitle}
          selectedPrompt={selectedPrompt}
          uploadedFile={uploadedFile}
        />
      )}

      {showDeleteConfirm && (
        <div className={style.deleteConfirmOverlay}>
          <div className={style.deleteConfirmDialog}>
            <div className={style.deleteConfirmHeader}>
              <h3>Delete Project</h3>
              <button
                className={style.closeButton}
                onClick={cancelDelete}
                aria-label="Close"
              >
                ✕
              </button>
            </div>
            <div className={style.deleteConfirmBody}>
              <p>
                Are you sure you want to delete this project? This action cannot
                be undone.
              </p>
            </div>
            <div className={style.deleteConfirmActions}>
              <button className={style.cancelButton} onClick={cancelDelete}>
                Cancel
              </button>
              <button className={style.confirmButton} onClick={confirmDelete}>
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {showWelcome && <WelcomeDialog onClose={handleWelcomeClose} />}
    </div>
  );
};

export default memo(HomePage);
