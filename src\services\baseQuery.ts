import { fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { BASE_URL, AI_BASE_URL } from './config';
import { getGlobalOpenDialog } from '../contexts/DialogContext';

// Global Axios instance with HTTPS enforcement
const axiosInstance = axios.create({
  // Ensure HTTPS is maintained and prevent automatic redirects to HTTP
  maxRedirects: 0, // Prevent automatic redirects that might downgrade protocol
  validateStatus: (status) => status < 400, // Accept redirects as errors to handle manually
});

// Add request interceptor to ensure HTTPS protocol
axiosInstance.interceptors.request.use(
  (config) => {
    // Log the request URL for debugging
    console.log('📤 Axios Request:', {
      url: config.url,
      method: config.method,
      protocol: config.url?.startsWith('https://')
        ? 'HTTPS'
        : config.url?.startsWith('http://')
          ? 'HTTP'
          : 'UNKNOWN',
    });

    // Ensure HTTPS protocol for production
    if (
      config.url &&
      config.url.startsWith('http://') &&
      !config.url.includes('localhost') &&
      !config.url.includes('127.0.0.1')
    ) {
      console.warn('⚠️ Converting HTTP to HTTPS for:', config.url);
      config.url = config.url.replace('http://', 'https://');
    }

    return config;
  },
  (error) => {
    console.error('❌ Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Add Axios interceptor for error handling
axiosInstance.interceptors.response.use(
  (response) => {
    console.log('📥 Axios Response:', {
      url: response.config.url,
      status: response.status,
      protocol: response.config.url?.startsWith('https://')
        ? 'HTTPS'
        : response.config.url?.startsWith('http://')
          ? 'HTTP'
          : 'UNKNOWN',
    });
    return response;
  },
  (error) => {
    console.error('❌ Axios Response Error:', {
      url: error.config?.url,
      status: error.response?.status,
      message: error.message,
    });

    if (error.response?.status === 401) {
      const openDialog = getGlobalOpenDialog();
      if (openDialog) {
        openDialog(); // Open the dialog when a 401 error occurs
      }
    }
    return Promise.reject(error);
  }
);

// Function to get the token
const getToken = (): string | undefined => {
  const token = localStorage.getItem('token');
  return token ? token.replace(/"/g, '') : undefined;
};

// Common baseQuery wrapper with interceptors and timeout handling
const createBaseQuery =
  (baseUrl: string) => async (args: any, api: any, extraOptions: any) => {
    const rawBaseQuery = fetchBaseQuery({
      baseUrl,
      timeout: 60000, // 60 second timeout for payment operations
      prepareHeaders: (headers) => {
        if (getToken()) {
          headers.set('Authorization', `Bearer ${getToken()}`);
        }
        return headers;
      },
    });

    // Perform the base query with timeout handling
    const result = await rawBaseQuery(args, api, extraOptions);

    // Enhanced error handling
    if (result.error) {
      const error = result.error as any;

      // Handle 401 authentication errors
      if (error?.status === 401) {
        const openDialog = getGlobalOpenDialog();
        if (openDialog) {
          openDialog();
        }
      }

      // Handle timeout errors specifically for payment operations
      if (error?.status === 504 || error?.status === 'TIMEOUT_ERROR') {
        console.error('Payment API timeout:', error);
        return {
          ...result,
          error: {
            ...error,
            data: {
              ...error.data,
              message:
                'Payment service is temporarily unavailable. Please try again in a few moments.',
              isTimeout: true,
            },
          },
        };
      }
    }

    return result;
  };

// Define base queries
export const baseQuery = createBaseQuery(BASE_URL);
export const baseQueryAi = createBaseQuery(AI_BASE_URL);

// Axios-based query for file uploads with progress tracking
export const fileUploadBaseQuery =
  ({ baseUrl }: { baseUrl: string }) =>
  async <T>({
    url,
    method,
    body,
    headers,
    onProgress,
  }: {
    url: string;
    method: string;
    body?: any;
    headers?: Record<string, string>;
    onProgress?: (progress: number, status: 'uploading' | 'processing') => void;
  }): Promise<{ data?: T; error?: string }> => {
    try {
      const fullUrl = `${baseUrl}${url}`;

      // Debug logging to track URL protocol
      console.log('🔍 File Upload Debug:', {
        baseUrl,
        url,
        fullUrl,
        protocol: fullUrl.startsWith('https://')
          ? 'HTTPS'
          : fullUrl.startsWith('http://')
            ? 'HTTP'
            : 'UNKNOWN',
      });

      const config: AxiosRequestConfig = {
        url: fullUrl,
        method,
        data: body,
        headers: {
          ...headers,
          Authorization: `Bearer ${getToken()}`,
        },
      };

      let lastProgress = 0;

      if (onProgress) {
        config.onUploadProgress = (progressEvent) => {
          if (progressEvent.total) {
            const currentProgress = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            if (currentProgress > lastProgress) {
              lastProgress = currentProgress;
              onProgress(currentProgress, 'uploading');
            }
          }
        };
      }

      const response: AxiosResponse<T> = await axiosInstance(config);

      // Call onProgress to signal processing is complete
      if (onProgress) {
        onProgress(100, 'processing');
      }

      return { data: response.data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  };
