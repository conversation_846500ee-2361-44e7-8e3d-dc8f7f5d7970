/**
 * Payment Endpoint Testing Utility
 * 
 * This utility helps test and debug payment API endpoints
 * to identify which ones are working and which are causing 404 errors.
 */

import { getCurrentUserId } from '../auth/userHelpers';

const BASE_URL = 'http://185.199.52.115:8182';

/**
 * Test if an endpoint is accessible
 */
export const testEndpoint = async (endpoint: string, method: string = 'GET'): Promise<{
  success: boolean;
  status: number;
  error?: string;
  data?: any;
}> => {
  try {
    const token = localStorage.getItem('token')?.replace(/"/g, '');
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`${BASE_URL}${endpoint}`, {
      method,
      headers,
    });

    const data = await response.json().catch(() => null);

    return {
      success: response.ok,
      status: response.status,
      data,
      error: response.ok ? undefined : `${response.status} ${response.statusText}`,
    };
  } catch (error: any) {
    return {
      success: false,
      status: 0,
      error: error.message,
    };
  }
};

/**
 * Test all payment-related endpoints
 */
export const testAllPaymentEndpoints = async () => {
  const userId = getCurrentUserId();
  
  const endpoints = [
    // Available endpoints (should work)
    { name: 'Get Plans', url: '/api/v1/plans/get-all', method: 'GET' },
    { name: 'Get Payment History', url: `/api/v1/payments/get-all?page=1&size=10${userId ? `&userId=${userId}` : ''}`, method: 'GET' },
    
    // Legacy endpoints (might not work)
    { name: 'Get Payment Methods (Legacy)', url: '/api/payments/methods', method: 'GET' },
    { name: 'Get Customer (Legacy)', url: '/api/payments/customers/details', method: 'GET' },
    { name: 'Get Payment Details (Legacy)', url: '/api/payments/details', method: 'GET' },
  ];

  console.log('=== Payment Endpoint Test Results ===');
  console.log('User ID:', userId);
  console.log('Base URL:', BASE_URL);
  console.log('');

  const results = [];

  for (const endpoint of endpoints) {
    console.log(`Testing: ${endpoint.name}`);
    console.log(`URL: ${BASE_URL}${endpoint.url}`);
    
    const result = await testEndpoint(endpoint.url, endpoint.method);
    results.push({ ...endpoint, ...result });
    
    if (result.success) {
      console.log('✅ SUCCESS:', result.status);
      if (result.data) {
        console.log('Data preview:', JSON.stringify(result.data).substring(0, 200) + '...');
      }
    } else {
      console.log('❌ FAILED:', result.error);
    }
    console.log('');
  }

  // Summary
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log('=== SUMMARY ===');
  console.log(`✅ Successful: ${successful.length}`);
  console.log(`❌ Failed: ${failed.length}`);
  
  if (failed.length > 0) {
    console.log('\nFailed endpoints:');
    failed.forEach(f => console.log(`- ${f.name}: ${f.error}`));
  }

  return results;
};

/**
 * Test payment initiation endpoint
 */
export const testPaymentInitiation = async (planId: string) => {
  const userId = getCurrentUserId();
  
  if (!userId) {
    console.error('Cannot test payment initiation: User not authenticated');
    return null;
  }

  const payload = {
    planId,
    userId,
  };

  console.log('Testing payment initiation...');
  console.log('Payload:', payload);

  try {
    const token = localStorage.getItem('token')?.replace(/"/g, '');
    const response = await fetch(`${BASE_URL}/api/v1/payments/initiate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Payment initiation successful:', data);
      return data;
    } else {
      console.log('❌ Payment initiation failed:', response.status, data);
      return null;
    }
  } catch (error) {
    console.error('❌ Payment initiation error:', error);
    return null;
  }
};

/**
 * Quick endpoint availability check
 */
export const quickEndpointCheck = async () => {
  const results = await Promise.all([
    testEndpoint('/api/v1/plans/get-all'),
    testEndpoint('/api/v1/payments/get-all?page=1&size=10'),
    testEndpoint('/api/payments/methods'), // This should fail
  ]);

  return {
    plansAvailable: results[0].success,
    paymentHistoryAvailable: results[1].success,
    legacyMethodsAvailable: results[2].success,
  };
};

// Export for console testing
(window as any).testPaymentEndpoints = testAllPaymentEndpoints;
(window as any).testPaymentInitiation = testPaymentInitiation;
(window as any).quickEndpointCheck = quickEndpointCheck;
